import React, {useState, useEffect} from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Alert,
  Platform,
} from 'react-native';
import {NativeModules} from 'react-native';

const {AppShortcut} = NativeModules;

const ShortcutDebugPanel = () => {
  const [debugInfo, setDebugInfo] = useState({});
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    loadDebugInfo();
  }, []);

  const loadDebugInfo = async () => {
    if (Platform.OS !== 'android') {
      setDebugInfo({error: 'Android only feature'});
      return;
    }

    try {
      console.log('Loading debug info...');
      console.log('AppShortcut module:', AppShortcut);
      console.log('Available methods:', Object.keys(AppShortcut || {}));

      if (!AppShortcut) {
        setDebugInfo({
          error: 'AppShortcut native module not found',
          platform: Platform.OS,
          androidVersion: Platform.Version,
          timestamp: new Date().toLocaleTimeString(),
        });
        return;
      }

      const supported = await AppShortcut.isShortcutSupported();
      const existing = await AppShortcut.getExistingShortcuts();

      setDebugInfo({
        platform: Platform.OS,
        androidVersion: Platform.Version,
        shortcutSupported: supported,
        existingShortcuts: existing,
        moduleAvailable: true,
        availableMethods: Object.keys(AppShortcut),
        timestamp: new Date().toLocaleTimeString(),
      });
    } catch (error) {
      console.error('Debug info error:', error);
      setDebugInfo({
        error: error.message,
        platform: Platform.OS,
        androidVersion: Platform.Version,
        moduleAvailable: !!AppShortcut,
        availableMethods: AppShortcut ? Object.keys(AppShortcut) : [],
        timestamp: new Date().toLocaleTimeString(),
      });
    }
  };

  const testShortcutCreation = async () => {
    setIsLoading(true);
    try {
      const shortcutId = `test_${Date.now()}`;
      const label = 'Test Shortcut';
      const deepLink = 'indiacustomercare://categories';

      console.log('Creating test shortcut:', {shortcutId, label, deepLink});
      console.log('AppShortcut module available:', !!AppShortcut);
      console.log('AppShortcut methods:', Object.keys(AppShortcut || {}));

      if (!AppShortcut) {
        throw new Error('AppShortcut native module not available');
      }

      if (!AppShortcut.createShortcut) {
        throw new Error(
          'createShortcut method not available on AppShortcut module',
        );
      }

      console.log('Calling AppShortcut.createShortcut...');
      const result = await AppShortcut.createShortcut(
        shortcutId,
        label,
        deepLink,
      );
      console.log('Shortcut creation completed with result:', result);

      Alert.alert('Test Result', `Shortcut creation result: ${result}`, [
        {text: 'OK'},
        {text: 'Refresh Info', onPress: loadDebugInfo},
      ]);
    } catch (error) {
      console.error('Test shortcut error:', error);
      Alert.alert(
        'Test Error',
        `Error: ${error.message}\n\nStack: ${error.stack || 'No stack trace'}`,
      );
    } finally {
      setIsLoading(false);
    }
  };

  const testLegacyShortcut = async () => {
    setIsLoading(true);
    try {
      // Force legacy shortcut creation
      const label = 'Legacy Test';
      const deepLink = 'indiacustomercare://categories';

      // Call legacy method directly if available
      const result = await AppShortcut.createShortcut(
        'legacy_test',
        label,
        deepLink,
      );

      Alert.alert('Legacy Test Result', `Result: ${result}`);
    } catch (error) {
      Alert.alert('Legacy Test Error', error.message);
    } finally {
      setIsLoading(false);
    }
  };

  const clearShortcuts = async () => {
    try {
      const existing = await AppShortcut.getExistingShortcuts();
      for (const shortcut of existing) {
        await AppShortcut.removeShortcut(shortcut.id);
      }
      Alert.alert('Success', 'All shortcuts cleared');
      loadDebugInfo();
    } catch (error) {
      Alert.alert('Error', error.message);
    }
  };

  if (Platform.OS !== 'android') {
    return (
      <View style={styles.container}>
        <Text style={styles.title}>Shortcut Debug Panel</Text>
        <Text style={styles.error}>
          This feature is only available on Android
        </Text>
      </View>
    );
  }

  return (
    <ScrollView style={styles.container}>
      <Text style={styles.title}>Shortcut Debug Panel</Text>

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>System Info</Text>
        <Text style={styles.info}>Platform: {debugInfo.platform}</Text>
        <Text style={styles.info}>
          Android Version: {debugInfo.androidVersion}
        </Text>
        <Text style={styles.info}>
          Module Available: {String(debugInfo.moduleAvailable)}
        </Text>
        <Text style={styles.info}>
          Shortcuts Supported: {String(debugInfo.shortcutSupported)}
        </Text>
        <Text style={styles.info}>
          Available Methods: {debugInfo.availableMethods?.join(', ') || 'None'}
        </Text>
        <Text style={styles.info}>Last Updated: {debugInfo.timestamp}</Text>
        {debugInfo.error && (
          <Text style={styles.error}>Error: {debugInfo.error}</Text>
        )}
      </View>

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Existing Shortcuts</Text>
        {debugInfo.existingShortcuts &&
        debugInfo.existingShortcuts.length > 0 ? (
          debugInfo.existingShortcuts.map((shortcut, index) => (
            <View key={index} style={styles.shortcutItem}>
              <Text style={styles.info}>ID: {shortcut.id}</Text>
              <Text style={styles.info}>Label: {shortcut.shortLabel}</Text>
            </View>
          ))
        ) : (
          <Text style={styles.info}>No existing shortcuts found</Text>
        )}
      </View>

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Test Actions</Text>

        <TouchableOpacity
          style={styles.button}
          onPress={loadDebugInfo}
          disabled={isLoading}>
          <Text style={styles.buttonText}>Refresh Info</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.button}
          onPress={testShortcutCreation}
          disabled={isLoading}>
          <Text style={styles.buttonText}>
            {isLoading ? 'Creating...' : 'Test Shortcut Creation'}
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.button}
          onPress={testLegacyShortcut}
          disabled={isLoading}>
          <Text style={styles.buttonText}>Test Legacy Shortcut</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.button, styles.dangerButton]}
          onPress={clearShortcuts}
          disabled={isLoading}>
          <Text style={styles.buttonText}>Clear All Shortcuts</Text>
        </TouchableOpacity>
      </View>

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Instructions</Text>
        <Text style={styles.instruction}>
          1. Check if shortcuts are supported on your device
        </Text>
        <Text style={styles.instruction}>
          2. Test shortcut creation and check your home screen
        </Text>
        <Text style={styles.instruction}>
          3. If shortcuts don't appear, try the legacy method
        </Text>
        <Text style={styles.instruction}>
          4. Check device settings for shortcut permissions
        </Text>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
    backgroundColor: '#f5f5f5',
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 20,
    color: '#333',
  },
  section: {
    backgroundColor: 'white',
    padding: 16,
    marginBottom: 16,
    borderRadius: 8,
    elevation: 2,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 12,
    color: '#333',
  },
  info: {
    fontSize: 14,
    marginBottom: 4,
    color: '#666',
  },
  error: {
    fontSize: 14,
    color: '#d32f2f',
    fontWeight: '500',
  },
  shortcutItem: {
    backgroundColor: '#f8f9fa',
    padding: 8,
    marginBottom: 8,
    borderRadius: 4,
  },
  button: {
    backgroundColor: '#007bff',
    padding: 12,
    borderRadius: 6,
    marginBottom: 8,
    alignItems: 'center',
  },
  dangerButton: {
    backgroundColor: '#dc3545',
  },
  buttonText: {
    color: 'white',
    fontWeight: '600',
    fontSize: 14,
  },
  instruction: {
    fontSize: 12,
    marginBottom: 4,
    color: '#666',
  },
});

export default ShortcutDebugPanel;
