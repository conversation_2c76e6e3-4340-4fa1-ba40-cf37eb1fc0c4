import {NativeModules, Platform, DeviceEventEmitter} from 'react-native';

const {AppShortcut} = NativeModules;

class AppShortcutService {
  constructor() {
    this.deepLinkListener = null;
  }

  /**
   * Check if app shortcuts are supported on this device
   * @returns {Promise<boolean>}
   */
  async isShortcutSupported() {
    if (Platform.OS !== 'android') {
      return false;
    }

    try {
      return await AppShortcut.isShortcutSupported();
    } catch (error) {
      console.error('Error checking shortcut support:', error);
      return false;
    }
  }

  /**
   * Create a pinned app shortcut
   * @param {string} shortcutId - Unique identifier for the shortcut
   * @param {string} label - Display label for the shortcut
   * @param {string} deepLink - Deep link URL to open when shortcut is tapped
   * @returns {Promise<string>}
   */
  async createShortcut(shortcutId, label, deepLink) {
    if (Platform.OS !== 'android') {
      throw new Error('App shortcuts are only supported on Android');
    }

    try {
      const result = await AppShortcut.createShortcut(shortcutId, label, deepLink);
      console.log('Shortcut creation result:', result);
      return result;
    } catch (error) {
      console.error('Error creating shortcut:', error);
      throw error;
    }
  }

  /**
   * Remove a dynamic shortcut
   * @param {string} shortcutId - Unique identifier for the shortcut to remove
   * @returns {Promise<string>}
   */
  async removeShortcut(shortcutId) {
    if (Platform.OS !== 'android') {
      throw new Error('App shortcuts are only supported on Android');
    }

    try {
      const result = await AppShortcut.removeShortcut(shortcutId);
      console.log('Shortcut removal result:', result);
      return result;
    } catch (error) {
      console.error('Error removing shortcut:', error);
      throw error;
    }
  }

  /**
   * Get list of existing dynamic shortcuts
   * @returns {Promise<Array>}
   */
  async getExistingShortcuts() {
    if (Platform.OS !== 'android') {
      return [];
    }

    try {
      const shortcuts = await AppShortcut.getExistingShortcuts();
      return shortcuts;
    } catch (error) {
      console.error('Error getting existing shortcuts:', error);
      return [];
    }
  }

  /**
   * Create a shortcut to Company List screen
   * @param {number} categoryId - Category ID to open
   * @param {string} categoryName - Category name for the shortcut label
   * @returns {Promise<string>}
   */
  async createCompanyListShortcut(categoryId, categoryName) {
    const shortcutId = `company_list_${categoryId}`;
    const label = `${categoryName} Companies`;
    const deepLink = `indiacustomercare://companylist?categoryId=${categoryId}&title=${encodeURIComponent(categoryName)}`;

    return this.createShortcut(shortcutId, label, deepLink);
  }

  /**
   * Create a shortcut to main Categories screen
   * @returns {Promise<string>}
   */
  async createCategoriesShortcut() {
    const shortcutId = 'categories_main';
    const label = 'Categories';
    const deepLink = 'indiacustomercare://categories';

    return this.createShortcut(shortcutId, label, deepLink);
  }

  /**
   * Set up deep link listener
   * @param {function} callback - Callback function to handle deep links
   */
  setupDeepLinkListener(callback) {
    if (Platform.OS !== 'android') {
      return;
    }

    // Remove existing listener if any
    this.removeDeepLinkListener();

    // Add new listener
    this.deepLinkListener = DeviceEventEmitter.addListener('deepLink', callback);
  }

  /**
   * Remove deep link listener
   */
  removeDeepLinkListener() {
    if (this.deepLinkListener) {
      this.deepLinkListener.remove();
      this.deepLinkListener = null;
    }
  }

  /**
   * Parse deep link URL and extract parameters
   * @param {string} url - Deep link URL
   * @returns {object} Parsed parameters
   */
  parseDeepLink(url) {
    try {
      const urlObj = new URL(url);
      const params = {};
      
      // Extract path
      params.path = urlObj.pathname.replace('/', '');
      
      // Extract query parameters
      urlObj.searchParams.forEach((value, key) => {
        params[key] = value;
      });

      return params;
    } catch (error) {
      console.error('Error parsing deep link:', error);
      return {};
    }
  }

  /**
   * Handle deep link navigation
   * @param {string} url - Deep link URL
   * @param {object} navigation - React Navigation object
   */
  handleDeepLinkNavigation(url, navigation) {
    const params = this.parseDeepLink(url);
    
    console.log('Handling deep link:', url, params);

    switch (params.path) {
      case 'companylist':
        if (params.categoryId && params.title) {
          navigation.navigate('CompanyScreen', {
            categoryId: parseInt(params.categoryId),
            title: decodeURIComponent(params.title),
          });
        }
        break;
      
      case 'categories':
        navigation.navigate('Categories');
        break;
      
      default:
        console.log('Unknown deep link path:', params.path);
        // Navigate to default screen
        navigation.navigate('Categories');
        break;
    }
  }
}

export default new AppShortcutService();
