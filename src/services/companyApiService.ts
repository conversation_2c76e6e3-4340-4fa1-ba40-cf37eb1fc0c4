import axios from 'axios';
import { CONFIG } from '../common/constant';

// Create an Axios instance with default settings for companies
const companyApi = axios.create({
  baseURL: CONFIG.API_URL,
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  },
  timeout: 60000, // 60 seconds timeout for large datasets
});

// Add request interceptor for logging
companyApi.interceptors.request.use(
  (config) => {
    console.log(`[CompanyAPI] Request: ${config.method?.toUpperCase()} ${config.url}`);
    return config;
  },
  (error) => {
    console.error('[CompanyAPI] Request error:', error);
    return Promise.reject(error);
  }
);

// Add response interceptor for logging
companyApi.interceptors.response.use(
  (response) => {
    console.log(`[CompanyAPI] Response: ${response.status} ${response.config.url}`);
    return response;
  },
  (error) => {
    console.error('[CompanyAPI] Response error:', error.response?.status, error.response?.data);
    return Promise.reject(error);
  }
);

// API Response interfaces
interface ApiResponse<T> {
  success: boolean;
  data: T;
  message?: string;
}

export interface ApiCompany {
  companyId: number;
  companyName: string;
  parentCompany?: string | null;
  companyEmail?: string | null;
  companyLogoUrl?: string | null;
  companyCountry?: string | null;
  companyAddress?: string | null;
  companyWebsite?: string | null;
  number?: string | null; // Phone number field
  upVoteCount?: number;
  downVoteCount?: number;
  createdAt?: string; // Note: API uses 'createdAt', not 'created_at'
  categories?: Array<{
    categoryId: number;
    name: string;
    iconUrl: string;
    isActive: boolean;
  }>;
}

export interface CompanyListResponse {
  message: string;
  data: {
    total: number;
    page: number;
    limit: number;
    companies: ApiCompany[];
  };
  status: number;
  success: boolean;
}

class CompanyApiService {
  /**
   * Fetch all companies with pagination support
   * This method handles the full pagination automatically
   */
  async fetchAllCompanies(): Promise<ApiCompany[]> {
    try {
      console.log('[CompanyAPI] Starting to fetch all companies with pagination...');
      const allCompanies: ApiCompany[] = [];
      let currentPage = 1;
      let totalPages = 1;
      const limit = 500; // Fetch 500 records per page for efficiency

      do {
        console.log(`[CompanyAPI] Fetching page ${currentPage}/${totalPages}...`);

        const pageResponse = await this.fetchCompaniesPage(currentPage, limit);

        // Extract companies from response
        const companies = pageResponse.data.companies;
        allCompanies.push(...companies);

        // Update pagination info from response data
        const total = pageResponse.data.total;
        totalPages = Math.ceil(total / limit);

        console.log(`[CompanyAPI] Page ${currentPage}/${totalPages} - Got ${companies.length} companies (Total so far: ${allCompanies.length}/${total})`);

        currentPage++;

        // Safety check to prevent infinite loops
        if (currentPage > 1000) {
          console.warn('[CompanyAPI] Safety limit reached, stopping pagination');
          break;
        }

      } while (currentPage <= totalPages);

      console.log(`[CompanyAPI] ✅ Fetched total ${allCompanies.length} companies across ${currentPage - 1} pages`);
      return allCompanies;

    } catch (error) {
      console.error('[CompanyAPI] Error fetching all companies:', error);
      throw error;
    }
  }

  /**
   * Fetch a single page of companies
   */
  async fetchCompaniesPage(page: number = 1, limit: number = 500, search: string = ''): Promise<CompanyListResponse> {
    try {
      const searchParam = search ? `&search=${encodeURIComponent(search)}` : '';
      const url = `/company/get-all?page=${page}&limit=${limit}&sortBy=created_at&sortOrder=DESC${searchParam}`;

      const response = await companyApi.get<CompanyListResponse>(url);

      console.log(`[CompanyAPI] Fetched page ${page} with ${response.data.data.companies.length} companies`);

      // Return the response data
      return response.data;
    } catch (error) {
      console.error(`[CompanyAPI] Error fetching companies page ${page}:`, error);
      throw error;
    }
  }

  async fetchCompaniesByCategory(categoryId: string, page: number = 1, limit: number = 20): Promise<CompanyListResponse> {
    try {
      const response = await companyApi.get<CompanyListResponse>(
        `/company?categoryId=${categoryId}&page=${page}&limit=${limit}&sortBy=created_at&sortOrder=DESC`
      );

      return response.data;
    } catch (error) {
      console.error('Error fetching companies by category:', error);
      throw error;
    }
  }

  async fetchCompanyById(companyId: string): Promise<ApiCompany> {
    try {
      const response = await companyApi.get<ApiResponse<ApiCompany>>(`/company/${companyId}`);

      // Handle different response formats
      if (response.data && 'data' in response.data) {
        return response.data.data;
      } else {
        return response.data as ApiCompany;
      }
    } catch (error) {
      console.error('Error fetching company by ID:', error);
      throw error;
    }
  }

  async searchCompanies(query: string, page: number = 1, limit: number = 20): Promise<CompanyListResponse> {
    try {
      const response = await companyApi.get<CompanyListResponse>(
        `/company?search=${encodeURIComponent(query)}&page=${page}&limit=${limit}&sortBy=created_at&sortOrder=DESC`
      );

      return response.data;
    } catch (error) {
      console.error('Error searching companies:', error);
      throw error;
    }
  }
}

export default new CompanyApiService();
