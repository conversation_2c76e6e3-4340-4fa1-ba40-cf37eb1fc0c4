import {useState, useEffect, useCallback} from 'react';
import {Alert, Platform} from 'react-native';
import AppShortcutService from '../services/AppShortcutService';

export const useAppShortcuts = (navigation) => {
  const [isShortcutSupported, setIsShortcutSupported] = useState(false);
  const [existingShortcuts, setExistingShortcuts] = useState([]);
  const [isLoading, setIsLoading] = useState(false);

  // Check if shortcuts are supported on device initialization
  useEffect(() => {
    const checkSupport = async () => {
      if (Platform.OS === 'android') {
        try {
          const supported = await AppShortcutService.isShortcutSupported();
          setIsShortcutSupported(supported);
        } catch (error) {
          console.error('Error checking shortcut support:', error);
          setIsShortcutSupported(false);
        }
      }
    };

    checkSupport();
  }, []);

  // Set up deep link listener
  useEffect(() => {
    if (Platform.OS === 'android' && navigation) {
      const handleDeepLink = (url) => {
        console.log('Deep link received:', url);
        AppShortcutService.handleDeepLinkNavigation(url, navigation);
      };

      AppShortcutService.setupDeepLinkListener(handleDeepLink);

      return () => {
        AppShortcutService.removeDeepLinkListener();
      };
    }
  }, [navigation]);

  // Load existing shortcuts
  const loadExistingShortcuts = useCallback(async () => {
    if (Platform.OS !== 'android') return;

    try {
      const shortcuts = await AppShortcutService.getExistingShortcuts();
      setExistingShortcuts(shortcuts);
    } catch (error) {
      console.error('Error loading existing shortcuts:', error);
    }
  }, []);

  // Create company list shortcut
  const createCompanyListShortcut = useCallback(async (categoryId, categoryName) => {
    if (!isShortcutSupported) {
      Alert.alert(
        'Not Supported',
        'App shortcuts are not supported on this device.'
      );
      return false;
    }

    setIsLoading(true);
    try {
      await AppShortcutService.createCompanyListShortcut(categoryId, categoryName);
      
      Alert.alert(
        'Shortcut Created',
        `A shortcut for "${categoryName} Companies" has been added to your home screen.`,
        [{text: 'OK'}]
      );

      // Reload existing shortcuts
      await loadExistingShortcuts();
      return true;
    } catch (error) {
      console.error('Error creating company list shortcut:', error);
      
      let errorMessage = 'Failed to create shortcut. Please try again.';
      if (error.message.includes('NOT_SUPPORTED')) {
        errorMessage = 'Pin shortcuts are not supported on this device.';
      }
      
      Alert.alert('Error', errorMessage);
      return false;
    } finally {
      setIsLoading(false);
    }
  }, [isShortcutSupported, loadExistingShortcuts]);

  // Create categories shortcut
  const createCategoriesShortcut = useCallback(async () => {
    if (!isShortcutSupported) {
      Alert.alert(
        'Not Supported',
        'App shortcuts are not supported on this device.'
      );
      return false;
    }

    setIsLoading(true);
    try {
      await AppShortcutService.createCategoriesShortcut();
      
      Alert.alert(
        'Shortcut Created',
        'A shortcut for "Categories" has been added to your home screen.',
        [{text: 'OK'}]
      );

      // Reload existing shortcuts
      await loadExistingShortcuts();
      return true;
    } catch (error) {
      console.error('Error creating categories shortcut:', error);
      
      let errorMessage = 'Failed to create shortcut. Please try again.';
      if (error.message.includes('NOT_SUPPORTED')) {
        errorMessage = 'Pin shortcuts are not supported on this device.';
      }
      
      Alert.alert('Error', errorMessage);
      return false;
    } finally {
      setIsLoading(false);
    }
  }, [isShortcutSupported, loadExistingShortcuts]);

  // Remove shortcut
  const removeShortcut = useCallback(async (shortcutId) => {
    if (!isShortcutSupported) return false;

    setIsLoading(true);
    try {
      await AppShortcutService.removeShortcut(shortcutId);
      
      Alert.alert(
        'Shortcut Removed',
        'The shortcut has been removed.',
        [{text: 'OK'}]
      );

      // Reload existing shortcuts
      await loadExistingShortcuts();
      return true;
    } catch (error) {
      console.error('Error removing shortcut:', error);
      Alert.alert('Error', 'Failed to remove shortcut. Please try again.');
      return false;
    } finally {
      setIsLoading(false);
    }
  }, [isShortcutSupported, loadExistingShortcuts]);

  // Check if a specific shortcut exists
  const shortcutExists = useCallback((shortcutId) => {
    return existingShortcuts.some(shortcut => shortcut.id === shortcutId);
  }, [existingShortcuts]);

  return {
    isShortcutSupported,
    existingShortcuts,
    isLoading,
    createCompanyListShortcut,
    createCategoriesShortcut,
    removeShortcut,
    loadExistingShortcuts,
    shortcutExists,
  };
};
