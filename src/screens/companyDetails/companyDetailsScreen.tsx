import React, {useState, useLayoutEffect, useEffect} from 'react';
import {
  StyleSheet,
  FlatList,
  SafeAreaView,
  View,
  Image,
  ScrollView,
  TouchableOpacity,
  Linking,
  Alert,
  Share,
  ActivityIndicator,
} from 'react-native';
import {Text, Button} from 'react-native-paper';
import CustomTollFreeNumbersCard from '../../components/CustomTollFreeNumbersCard';
import {Images} from '../../assets';
import {useNavigation} from '@react-navigation/native';
import {COLORS, FONTS, CONFIG} from '../../common/constant';
import commonStyles from '../../common/commonStyles';
import NoteModalScreen from '../note/noteScreen';
import {apiRequest} from '../../networking/httpServices.js';
import {showErrorToast} from '../../utils/showToast.js';
import CustomSearchBar from '../../components/CustomSearchBar';
import {useNetworkState} from '../../utils/networkStateManager';
import historyRepository from '../../database/watermelon/repositories/historyRepository';
import companyRepository from '../../database/watermelon/repositories/companyRepository';

// Contact number interface
export interface ContactNumber {
  numberId: number;
  companyId: number;
  number: string;
  description: string;
  type: string;
  upvoteCount: number;
  downvoteCount: number;
  isWhatsapp: boolean;
}

// Company details interface
export interface CompanyDetails {
  companyId: number;
  companyName: string;
  parentCompany: string | null;
  companyEmail: string | null;
  companyLogoUrl: string | null;
  companyCountry: string | null;
  companyAddress: string | null;
  companyWebsite: string | null;
  number: string | null; // Phone number field
  upVoteCount: number;
  downVoteCount: number;
  createdAt: string;
  categories: Array<{
    categoryId: number;
    name: string;
    iconUrl: string;
    isActive: boolean;
  }>;
  contactNumbers: {
    TOLL_FREE: ContactNumber[];
    ALL_INDIA: ContactNumber[];
    INTERNATIONAL: ContactNumber[];
  };
}

const CompanyDetailsScreen = ({route}: {route: any}) => {
  const navigation = useNavigation();
  const [searchQuery, setSearchQuery] = useState('');
  const {isConnected} = useNetworkState();
  const [error, setError] = useState<string | null>(null);

  const [tollFreeCenter, setTollFreeCenter] = useState(false);
  const [indiaCallCenter, setIndiaCallCenter] = useState(false);
  const [internationalCenter, setInternationalCenter] = useState(false);

  const [tollFreeNumbers, setTollFreeNumbers] = useState<ContactNumber[]>([]);
  const [allIndiaNumbers, setAllIndiaNumbers] = useState<ContactNumber[]>([]);
  const [internationalNumber, setInternationalNumber] = useState<
    ContactNumber[]
  >([]);

  const [isModalVisible, setModalVisible] = useState(false);
  const [loading, setLoading] = useState(false);
  const [companyInfo, setCompanyInfo] = useState<CompanyDetails | null>(null);
  const {title, companyId = '', fromHistory = false} = route.params;
  const [isSearching, setIsSearching] = useState(false);
  // Add state to track if current company is in Bank category and holiday message
  const [isBankCategory, setIsBankCategory] = useState(false);
  const [bankHolidayMessage, setBankHolidayMessage] = useState('');

  // Utility functions for bank holiday detection
  const getNthSaturdayOfMonth = (
    year: number,
    month: number,
    n: number,
  ): Date => {
    const firstDay = new Date(year, month, 1);
    const firstSaturday = new Date(firstDay);

    // Find first Saturday of the month
    const daysToSaturday = (6 - firstDay.getDay()) % 7;
    firstSaturday.setDate(1 + daysToSaturday);

    // Calculate nth Saturday
    const nthSaturday = new Date(firstSaturday);
    nthSaturday.setDate(firstSaturday.getDate() + (n - 1) * 7);

    return nthSaturday;
  };

  const isNationalHoliday = (date: Date): string | null => {
    const day = date.getDate();
    const month = date.getMonth() + 1; // getMonth() returns 0-11

    if (month === 1 && day === 26) return 'Republic Day';
    if (month === 8 && day === 15) return 'Independence Day';
    if (month === 10 && day === 2) return 'Gandhi Jayanti';

    return null;
  };

  const isBankHoliday = (date: Date): {isHoliday: boolean; message: string} => {
    const year = date.getFullYear();
    const month = date.getMonth();
    const day = date.getDate();

    // Check for national holidays
    const nationalHoliday = isNationalHoliday(date);
    if (nationalHoliday) {
      return {
        isHoliday: true,
        message: `Banks are closed today for ${nationalHoliday}`,
      };
    }

    // Check for 2nd and 4th Saturday
    const secondSaturday = getNthSaturdayOfMonth(year, month, 2);
    const fourthSaturday = getNthSaturdayOfMonth(year, month, 4);

    if (day === secondSaturday.getDate() && date.getDay() === 6) {
      return {
        isHoliday: true,
        message: 'Banks are closed today (2nd Saturday of the month)',
      };
    }

    if (day === fourthSaturday.getDate() && date.getDay() === 6) {
      return {
        isHoliday: true,
        message: 'Banks are closed today (4th Saturday of the month)',
      };
    }

    return {isHoliday: false, message: ''};
  };

  // Check if current company belongs to Bank category and if today is a bank holiday
  const checkBankHolidayStatus = (companyData: CompanyDetails | null) => {
    if (!companyData || !companyData.categories) {
      setIsBankCategory(false);
      setBankHolidayMessage('');
      return;
    }

    // Check if company belongs to Bank category (categoryId = 2)
    const isBankCompany = companyData.categories.some(
      category => category.categoryId === 2,
    );

    if (!isBankCompany) {
      setIsBankCategory(false);
      setBankHolidayMessage('');
      console.log(
        `[CompanyDetailsScreen] Company ${companyData.companyName} is not in Bank category`,
      );
      return;
    }

    // Check if today is a bank holiday
    const today = new Date();
    const holidayStatus = isBankHoliday(today);

    setIsBankCategory(holidayStatus.isHoliday);
    setBankHolidayMessage(holidayStatus.message);

    console.log(
      `[CompanyDetailsScreen] Company ${companyData.companyName} is in Bank category. Holiday status: ${holidayStatus.isHoliday}`,
    );

    if (holidayStatus.isHoliday) {
      console.log(
        `[CompanyDetailsScreen] Bank holiday message: ${holidayStatus.message}`,
      );
    }
  };

  const getComapnyDetails = async () => {
    setLoading(true);
    setError(null);
    try {
      const result = await apiRequest(
        `${CONFIG.API_URL}/company/${companyId}`,
        {
          method: 'GET',
          headers: {
            Accept: 'application/json',
            'Content-Type': 'application/json',
          },
        },
      );
      if (result.success && result.data) {
        setCompanyInfo(result.data);

        // Check bank holiday status after setting company info
        checkBankHolidayStatus(result.data);

        // Save company details to local database for history
        try {
          // Convert API company format to local database format
          const companyForDb = {
            company_id: result.data.companyId, // Store original API companyId
            company_name: result.data.companyName,
            parent_company: result.data.parentCompany,
            company_email: result.data.companyEmail,
            company_logo_url: result.data.companyLogoUrl,
            company_country: result.data.companyCountry,
            company_address: result.data.companyAddress,
            company_website: result.data.companyWebsite,
            number: result.data.number, // Store phone number
            upvote_count: result.data.upVoteCount,
            downvote_count: result.data.downVoteCount,
          };

          // Use createOrUpdate to handle both create and update cases
          await companyRepository.createOrUpdate(companyForDb);
          console.log(
            'Saved/Updated company in local database:',
            result.data.companyId,
          );
        } catch (dbError) {
          console.error('Failed to save company to local database:', dbError);
        }

        // Store contact numbers and determine which section to open by default
        if (result.data.contactNumbers) {
          let hasTollFree = false;
          let hasAllIndia = false;
          let hasInternational = false;

          if (
            result.data.contactNumbers.TOLL_FREE &&
            result.data.contactNumbers.TOLL_FREE.length > 0
          ) {
            setTollFreeNumbers(result.data.contactNumbers.TOLL_FREE);
            hasTollFree = true;
          }

          if (
            result.data.contactNumbers.ALL_INDIA &&
            result.data.contactNumbers.ALL_INDIA.length > 0
          ) {
            setAllIndiaNumbers(result.data.contactNumbers.ALL_INDIA);
            hasAllIndia = true;
          }

          if (
            result.data.contactNumbers.INTERNATIONAL &&
            result.data.contactNumbers.INTERNATIONAL.length > 0
          ) {
            setInternationalNumber(result.data.contactNumbers.INTERNATIONAL);
            hasInternational = true;
          }

          // Set default open section - prioritize Toll Free if available
          if (hasTollFree) {
            setTollFreeCenter(true);
          } else if (hasAllIndia) {
            setIndiaCallCenter(true);
          } else if (hasInternational) {
            setInternationalCenter(true);
          }
        }
      }
    } catch (err: any) {
      showErrorToast('Failed to load company details');
      setError(err.message || 'Failed to load company details');
    } finally {
      setLoading(false);
    }
  };

  // Add company to history when viewed
  const addToHistory = async () => {
    if (companyId && !fromHistory) {
      try {
        // Convert companyId to string for WatermelonDB
        const companyIdString = companyId.toString();

        await historyRepository.addToHistory(companyIdString);
        console.log('Company added to history:', companyIdString);
      } catch (error) {
        console.error('Failed to add company to history:', error);
      }
    }
  };

  useEffect(() => {
    getComapnyDetails();
  }, []);

  useEffect(() => {
    if (isConnected && companyId) {
      getComapnyDetails();
    }
  }, [isConnected]);

  // Add to history when company details are loaded
  useEffect(() => {
    if (companyInfo && !fromHistory) {
      addToHistory();
    }
  }, [companyInfo]);

  useLayoutEffect(() => {
    navigation.setOptions({
      title,
      headerRight: () => (
        <View style={styles.headerIconsContainer}>
          <TouchableOpacity onPress={() => handleNotePress()}>
            <Image source={Images.ic_note} style={styles.iconStyle} />
          </TouchableOpacity>

          <TouchableOpacity onPress={() => handleSharePress()}>
            <Image source={Images.ic_shareFat} style={styles.iconStyle} />
          </TouchableOpacity>
        </View>
      ),
    });
  }, [navigation, title]);

  const handleNotePress = () => {
    // Handle Note icon press
    setModalVisible(!isModalVisible);
  };

  const handleSharePress = () => {
    // Handle Share icon press
    sharingApp();
  };

  const sharingApp = async () => {
    try {
      const result = await Share.share({
        message:
          'https://www.figma.com/design/igcyn1kD9Brfw4RBlsuR6Q/India-Customer-Care-App---UI-UX-Design?node-id=54-291&t=isRgCzk2UcAOSNsv-0',
      });

      if (result.action === Share.sharedAction) {
        if (result.activityType) {
          console.log('Shared with activity type:', result.activityType);
        } else {
          console.log('Shared successfully');
        }
      } else if (result.action === Share.dismissedAction) {
        console.log('Share dismissed');
      }
    } catch (error) {
      Alert.alert('Error', 'An unexpected error occurred while sharing.');
    }
  };

  // Handle search
  const handleSearch = (query: string) => {
    setSearchQuery(query);

    if (!query || query.trim() === '') {
      // Reset to original data from companyInfo when search is cleared
      if (companyInfo?.contactNumbers) {
        // Reset the data
        setTollFreeNumbers(companyInfo.contactNumbers.TOLL_FREE || []);
        setAllIndiaNumbers(companyInfo.contactNumbers.ALL_INDIA || []);
        setInternationalNumber(companyInfo.contactNumbers.INTERNATIONAL || []);

        // Reset the expanded sections to their default state
        // We'll reuse the same logic as in getComapnyDetails
        const hasTollFree =
          companyInfo.contactNumbers.TOLL_FREE &&
          companyInfo.contactNumbers.TOLL_FREE.length > 0;
        const hasAllIndia =
          companyInfo.contactNumbers.ALL_INDIA &&
          companyInfo.contactNumbers.ALL_INDIA.length > 0;
        const hasInternational =
          companyInfo.contactNumbers.INTERNATIONAL &&
          companyInfo.contactNumbers.INTERNATIONAL.length > 0;

        // Reset all sections to closed first
        setTollFreeCenter(false);
        setIndiaCallCenter(false);
        setInternationalCenter(false);

        // Then open the appropriate one
        if (hasTollFree) {
          setTollFreeCenter(true);
        } else if (hasAllIndia) {
          setIndiaCallCenter(true);
        } else if (hasInternational) {
          setInternationalCenter(true);
        }
      }
      return;
    }

    // Filter contact numbers based on search query
    const filteredNumbers = {
      tollFree:
        companyInfo?.contactNumbers?.TOLL_FREE?.filter(
          item =>
            item.number.includes(query) ||
            item.description.toLowerCase().includes(query.toLowerCase()),
        ) || [],
      allIndia:
        companyInfo?.contactNumbers?.ALL_INDIA?.filter(
          item =>
            item.number.includes(query) ||
            item.description.toLowerCase().includes(query.toLowerCase()),
        ) || [],
      international:
        companyInfo?.contactNumbers?.INTERNATIONAL?.filter(
          item =>
            item.number.includes(query) ||
            item.description.toLowerCase().includes(query.toLowerCase()),
        ) || [],
    };

    setTollFreeNumbers(filteredNumbers.tollFree);
    setAllIndiaNumbers(filteredNumbers.allIndia);
    setInternationalNumber(filteredNumbers.international);

    // Determine which section to show based on search results
    const hasTollFreeResults = filteredNumbers.tollFree.length > 0;
    const hasAllIndiaResults = filteredNumbers.allIndia.length > 0;
    const hasInternationalResults = filteredNumbers.international.length > 0;

    // Reset all sections to closed first
    setTollFreeCenter(false);
    setIndiaCallCenter(false);
    setInternationalCenter(false);

    // Then open the appropriate one based on search results
    if (hasTollFreeResults) {
      setTollFreeCenter(true);
    } else if (hasAllIndiaResults) {
      setIndiaCallCenter(true);
    } else if (hasInternationalResults) {
      setInternationalCenter(true);
    }
  };

  const handleComplaintTapped = () => {};

  // Render a single company item
  const renderTollNumberItem = ({item}: {item: ContactNumber}) => (
    <CustomTollFreeNumbersCard numberData={item} />
  );

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView>
        {/* Show Bank Holidays label only for Bank category companies and on specific holidays */}
        {isBankCategory && bankHolidayMessage && (
          <View style={{backgroundColor: '#F89696'}}>
            <Text style={commonStyles.labelTopInstructor}>
              {bankHolidayMessage}
            </Text>
          </View>
        )}
        {/* Temporarily hidden complaint information
        <View style={{paddingLeft: 15, paddingRight: 15}}>
          <Text
            style={[
              commonStyles.instructionText,
              commonStyles.instructionTextTopSpace,
            ]}>
            For Cyber Fraud Complaint: 1930
          </Text>
          <Text
            style={[
              commonStyles.instructionText,
              commonStyles.instructionTextBottomSpace,
            ]}>
            For Complaint Through RBI: 14440
          </Text>
        </View>
        */}
        <View style={{marginTop: 10}} />
        <CustomSearchBar
          onSearch={handleSearch}
          isSearching={isSearching}
          initialValue={searchQuery}
          placeholder="Search numbers"
        />
        {loading && (
          <View style={commonStyles.footerLoader}>
            <ActivityIndicator size="small" color="#0000ff" />
            <Text style={commonStyles.footerText}>Loading more...</Text>
          </View>
        )}
        {error && (
          <View style={commonStyles.errorContainer}>
            <Text style={commonStyles.errorText}>{error}</Text>
            <TouchableOpacity
              style={styles.retryButton}
              onPress={() => getComapnyDetails()}>
              <Text style={styles.retryButtonText}>Retry</Text>
            </TouchableOpacity>
          </View>
        )}
        {tollFreeNumbers.length > 0 && (
          <View style={styles.itemCellView}>
            <TouchableOpacity
              style={styles.sectionHeader}
              onPress={() => setTollFreeCenter(!tollFreeCenter)}
              activeOpacity={0.7}>
              <Text style={styles.itemHeader}>Toll Free Numbers</Text>
              <View style={styles.cellHeaderIconContainer}>
                <Image
                  style={{
                    height: 25,
                    width: 25,
                  }}
                  source={
                    tollFreeCenter ? Images.ic_caretUp : Images.ic_caretDown
                  }
                />
              </View>
            </TouchableOpacity>
            {tollFreeCenter && tollFreeNumbers.length > 0 && (
              <FlatList
                style={styles.itemView}
                scrollEnabled={false}
                data={tollFreeNumbers}
                keyExtractor={item => item.numberId.toString()}
                renderItem={renderTollNumberItem}
                showsVerticalScrollIndicator={false}
                showsHorizontalScrollIndicator={false}
              />
            )}
          </View>
        )}
        {allIndiaNumbers.length > 0 && (
          <View style={styles.itemCellView}>
            <TouchableOpacity
              style={styles.sectionHeader}
              onPress={() => setIndiaCallCenter(!indiaCallCenter)}
              activeOpacity={0.7}>
              <Text style={styles.itemHeader}>All India Number</Text>
              <View style={styles.cellHeaderIconContainer}>
                <Image
                  style={{
                    height: 25,
                    width: 25,
                  }}
                  source={
                    indiaCallCenter ? Images.ic_caretUp : Images.ic_caretDown
                  }
                />
              </View>
            </TouchableOpacity>
            {indiaCallCenter && (
              <FlatList
                style={styles.itemView}
                scrollEnabled={false}
                data={allIndiaNumbers}
                keyExtractor={item => item.numberId.toString()}
                renderItem={renderTollNumberItem}
                showsVerticalScrollIndicator={false}
                showsHorizontalScrollIndicator={false}
              />
            )}
          </View>
        )}
        {internationalNumber.length > 0 && (
          <View style={styles.itemCellView}>
            <TouchableOpacity
              style={styles.sectionHeader}
              onPress={() => setInternationalCenter(!internationalCenter)}
              activeOpacity={0.7}>
              <Text style={styles.itemHeader}>International Number</Text>
              <View style={styles.cellHeaderIconContainer}>
                <Image
                  style={{
                    height: 25,
                    width: 25,
                  }}
                  source={
                    internationalCenter
                      ? Images.ic_caretUp
                      : Images.ic_caretDown
                  }
                />
              </View>
            </TouchableOpacity>
            {internationalCenter && (
              <FlatList
                style={styles.itemView}
                scrollEnabled={false}
                data={internationalNumber}
                keyExtractor={item => item.numberId.toString()}
                renderItem={renderTollNumberItem}
                showsVerticalScrollIndicator={false}
                showsHorizontalScrollIndicator={false}
              />
            )}
          </View>
        )}
        <Button
          mode="contained"
          onPress={handleComplaintTapped}
          style={styles.saveButton}
          labelStyle={styles.saveButtonLabel}
          buttonColor="#0a1d50">
          For Complaint
        </Button>
        <View>
          <View style={{flex: 1}}>
            <View style={styles.visitSiteView}>
              <Text style={styles.visitSiteKey}>Visit Official Site:</Text>
              <TouchableOpacity
                onPress={() => {
                  Linking.openURL(companyInfo?.companyWebsite ?? '');
                }}>
                <Text
                  numberOfLines={0}
                  ellipsizeMode="tail"
                  style={styles.siteLink}>
                  {companyInfo?.companyName}
                </Text>
              </TouchableOpacity>
            </View>
          </View>
          {/* Temporarily hidden social media icons
          <View style={styles.imageContainer}>
            <TouchableOpacity style={styles.imageTouchable}>
              <Image source={Images.ic_fb} style={styles.image} />
            </TouchableOpacity>
            <TouchableOpacity style={styles.imageTouchable}>
              <Image source={Images.ic_insta} style={styles.image} />
            </TouchableOpacity>
            <TouchableOpacity style={styles.imageTouchable}>
              <Image source={Images.ic_x} style={styles.image} />
            </TouchableOpacity>
            <TouchableOpacity style={styles.imageTouchable}>
              <Image source={Images.ic_mail} style={styles.image} />
            </TouchableOpacity>
          </View>
          */}
        </View>
      </ScrollView>
      <NoteModalScreen
        visible={isModalVisible}
        title="Categories Modal"
        content="This is a sample modal message."
        onClose={() => setModalVisible(false)}
      />
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#ffffff',
    marginBottom: 0,
  },
  itemCellView: {
    backgroundColor: COLORS.WHITE,
    borderWidth: 1,
    borderRadius: 8,
    borderColor: '#D7E2F1',
    marginLeft: 15,
    marginRight: 15,
    marginTop: 15,
    padding: 3,
  },
  itemView: {
    paddingLeft: 15,
    paddingRight: 15,
    paddingBottom: 15,
    paddingTop: 6,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 5,
    paddingHorizontal: 5,
  },
  cellHeaderIconContainer: {
    height: 40,
    width: 40,
    marginRight: 10,
    alignItems: 'center',
    justifyContent: 'center',
  },
  saveButton: {
    margin: 15,
    marginTop: 30,
    paddingVertical: 6,
    borderRadius: 10,
  },
  saveButtonLabel: {
    fontSize: 16,
    color: COLORS.WHITE,
    fontFamily: FONTS.POPPINS.MEDIUM,
  },
  itemHeader: {
    marginLeft: 15,
    marginTop: 10,
    fontFamily: FONTS.POPPINS.MEDIUM,
    fontSize: 23,
  },
  imageContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 10,
  },
  imageTouchable: {
    height: 40,
    width: 40,
  },
  image: {
    height: 40,
    width: 40,
    resizeMode: 'contain',
  },
  headerIconsContainer: {
    flexDirection: 'row',
  },
  iconStyle: {
    width: 24,
    height: 24,
    marginHorizontal: 10,
  },
  visitSiteView: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    overflow: 'hidden',
    marginHorizontal: 10,
    marginBottom: 10,
    padding: 10,
    borderRadius: 6,
    flexWrap: 'wrap',
  },
  visitSiteKey: {
    fontFamily: FONTS.POPPINS.SEMI_BOLD,
    fontSize: 17,
    color: '#000',
  },
  siteLink: {
    marginLeft: 5,
    fontFamily: FONTS.POPPINS.SEMI_BOLD,
    fontSize: 16,
    color: '#007FF5',
    textDecorationLine: 'underline',
    maxWidth: '90%',
  },
  retryButton: {
    backgroundColor: '#0a1d50',
    padding: 10,
    borderRadius: 5,
    marginTop: 10,
  },
  retryButtonText: {
    color: '#fff',
    fontSize: 16,
    textAlign: 'center',
  },
});

export default CompanyDetailsScreen;
