import React, {useState, useEffect, useCallback} from 'react';
import {
  StyleSheet,
  FlatList,
  SafeAreaView,
  View,
  Text,
  ActivityIndicator,
  Modal,
  TouchableOpacity,
} from 'react-native';
import CustomSearchBar from '../../components/CustomSearchBar';
import CustomCategoriesCard from '../../components/CustomCategoriesCard';
import ShortcutButton from '../../components/ShortcutButton';
import ShortcutDebugPanel from '../../components/ShortcutDebugPanel'; // TEMPORARY FOR TESTING
import SyncStatusIndicator from '../../components/SyncStatusIndicator';
import DatabaseTestPanel from '../../components/DatabaseTestPanel'; // TEMPORARY FOR TESTING
import commonStyles from '../../common/commonStyles';
import {debounce} from 'lodash';
import {showErrorToast} from '../../utils/showToast';
import watermelonCategoryRepository, {
  CategoryData,
} from '../../database/watermelon/repositories/categoryRepository';
import {useBackgroundSync} from '../../hooks/useBackgroundSync';
import {useAppNavigation} from '../../hooks/useAppNavigation';
import {useAppShortcuts} from '../../hooks/useAppShortcuts';

const CategoriesScreen = () => {
  // Use background sync hook to monitor sync status
  const categoriesSync = useBackgroundSync('categories');

  // Navigation and shortcuts
  const navigation = useAppNavigation();
  const {
    isShortcutSupported,
    createCategoriesShortcut,
    isLoading: shortcutLoading,
  } = useAppShortcuts(navigation);

  const [searchQuery, setSearchQuery] = useState('');
  const [loading, setLoading] = useState(true);

  const [originalCategoryList, setOriginalCategoryList] = useState<
    CategoryData[]
  >([]); // Store the original list
  const [categoryList, setCategoryList] = useState<CategoryData[]>([]);
  const [refreshing, setRefreshing] = useState(false);

  // TEMPORARY: Test panel state
  const [showTestPanel, setShowTestPanel] = useState(false);
  const [showShortcutDebug, setShowShortcutDebug] = useState(false);

  // Load data from local database on component mount
  useEffect(() => {
    loadCategoriesFromLocalDB();
  }, []);

  // Reload data when background sync completes
  useEffect(() => {
    if (categoriesSync.status === 'completed') {
      loadCategoriesFromLocalDB();
    }
  }, [categoriesSync.status]);

  // Reload data when search query changes
  useEffect(() => {
    if (originalCategoryList.length > 0) {
      loadCategoriesFromLocalDB();
    }
  }, [searchQuery]);

  // Load categories from local database only
  const loadCategoriesFromLocalDB = async () => {
    setLoading(true);
    try {
      const localCategories = await watermelonCategoryRepository.getAll();

      console.log('Loaded categories from local DB:', localCategories.length);

      // Deduplicate categories by ID
      const uniqueCategories = Array.from(
        new Map(localCategories.map(item => [item.categoryId, item])).values(),
      );

      // Sort categories alphabetically by name (A-Z)
      const sortedCategories = localCategories.sort((a, b) =>
        a.name.localeCompare(b.name, undefined, {sensitivity: 'base'}),
      );

      // Save the original sorted list for search filtering
      setOriginalCategoryList(sortedCategories);

      // Apply search filter if query exists
      if (searchQuery && searchQuery.length >= 3) {
        const filteredCategories = sortedCategories.filter(
          (category: CategoryData) =>
            category.name.toLowerCase().includes(searchQuery.toLowerCase()),
        );
        // Sort filtered results as well
        const sortedFilteredCategories = filteredCategories.sort((a, b) =>
          a.name.localeCompare(b.name, undefined, {sensitivity: 'base'}),
        );
        setCategoryList(sortedFilteredCategories);
      } else {
        setCategoryList(sortedCategories);
      }
    } catch (err) {
      console.error('Error loading categories from local DB:', err);
      showErrorToast('Failed to load categories from local storage');
      setCategoryList([]);
      setOriginalCategoryList([]);
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const filterSearchedData = (searchText: string) => {
    console.log('Filtering categories with search text:', searchText);
    console.log('Original category list:', originalCategoryList);

    if (!searchText || searchText.trim() === '') {
      // Restore the original list if search text is empty (already sorted)
      console.log('Restoring original category list');
      setCategoryList([...originalCategoryList]); // Ensure a new array reference
      return;
    }

    // Filter categories based on the search text (case-insensitive)
    const filteredCategories = originalCategoryList.filter(category =>
      category.name.toLowerCase().includes(searchText.toLowerCase()),
    );

    // Sort filtered results alphabetically (A-Z)
    const sortedFilteredCategories = filteredCategories.sort((a, b) =>
      a.name.localeCompare(b.name, undefined, {sensitivity: 'base'}),
    );

    setCategoryList(sortedFilteredCategories);
  };

  const debouncedSearch = useCallback(
    debounce((text: string) => {
      console.log('Debounced search text:', text);
      filterSearchedData(text);
    }, 300),
    [originalCategoryList],
  );

  const handleSearchChange = (text: string) => {
    setSearchQuery(text);
    debouncedSearch(text);
  };

  // Render empty list message
  const renderEmptyList = () => {
    if (loading && !refreshing) return null;

    return (
      <View style={commonStyles.emptyContainer}>
        <Text style={commonStyles.emptyText}>
          {searchQuery
            ? 'No categories match your search.'
            : 'No categories found.'}
        </Text>
      </View>
    );
  };

  // Render footer (loading indicator for pagination)
  const renderFooter = () => {
    if (!loading || refreshing) return null;
    return (
      <View style={commonStyles.footerLoader}>
        <ActivityIndicator size="large" color="#0000ff" />
        <Text style={commonStyles.footerText}>Loading more categories...</Text>
      </View>
    );
  };

  // Handle refresh - simply reload from local database
  const handleRefresh = () => {
    setRefreshing(true);
    loadCategoriesFromLocalDB();
  };

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.mainContent}>
        <CustomSearchBar
          onSearch={handleSearchChange}
          onVoiceResult={result => {
            setSearchQuery(result);
            handleSearchChange(result);
          }}
          isSearching={categoriesSync.isLoading}
          initialValue={searchQuery}
          placeholder="Search categories"
          showVoiceSearch={true}
        />

        {/* Add to Home Screen shortcut button */}
        {isShortcutSupported && (
          <View style={{paddingHorizontal: 15, paddingTop: 10}}>
            <ShortcutButton
              title="Add Categories to Home Screen"
              onPress={createCategoriesShortcut}
              isLoading={shortcutLoading}
            />
          </View>
        )}

        {/* TEMPORARY: Shortcut Debug Button */}
        <TouchableOpacity
          style={[styles.testButton, {backgroundColor: '#28a745'}]}
          onPress={() => setShowShortcutDebug(true)}>
          <Text style={styles.testButtonText}>🔧 Shortcut Debug</Text>
        </TouchableOpacity>

        {/* TEMPORARY: Test Panel Button - Hidden for now
        <TouchableOpacity
          style={styles.testButton}
          onPress={() => setShowTestPanel(true)}>
          <Text style={styles.testButtonText}>🧪 DB Test Panel</Text>
        </TouchableOpacity>
        */}
        {/* Sync status indicator temporarily hidden - background sync continues */}
        {/* <SyncStatusIndicator showOnlyWhenActive={true} /> */}

        <FlatList
          style={{padding: 15, flex: 1}}
          data={categoryList}
          keyExtractor={item => (item.categoryId ? item.categoryId : '')}
          renderItem={({item}) => <CustomCategoriesCard item={item} />}
          showsVerticalScrollIndicator={false}
          showsHorizontalScrollIndicator={false}
          onEndReachedThreshold={0.3}
          ListEmptyComponent={renderEmptyList}
          ListFooterComponent={renderFooter}
          refreshing={refreshing}
          onRefresh={handleRefresh}
          contentContainerStyle={
            categoryList.length === 0 ? commonStyles.fullHeight : null
          }
        />
      </View>

      {/* TEMPORARY: Shortcut Debug Modal */}
      <Modal
        visible={showShortcutDebug}
        animationType="slide"
        presentationStyle="pageSheet">
        <SafeAreaView style={styles.modalContainer}>
          <View style={styles.modalHeader}>
            <TouchableOpacity
              style={styles.closeButton}
              onPress={() => setShowShortcutDebug(false)}>
              <Text style={styles.closeButtonText}>✕ Close</Text>
            </TouchableOpacity>
          </View>
          <ShortcutDebugPanel />
        </SafeAreaView>
      </Modal>

      {/* TEMPORARY: Test Panel Modal */}
      <Modal
        visible={showTestPanel}
        animationType="slide"
        presentationStyle="pageSheet">
        <SafeAreaView style={styles.modalContainer}>
          <View style={styles.modalHeader}>
            <TouchableOpacity
              style={styles.closeButton}
              onPress={() => setShowTestPanel(false)}>
              <Text style={styles.closeButtonText}>✕ Close</Text>
            </TouchableOpacity>
          </View>
          <DatabaseTestPanel />
        </SafeAreaView>
      </Modal>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  mainContent: {
    flex: 1,
  },
  microphoneButton: {
    marginLeft: 10,
  },
  microphoneIcon: {
    height: 27,
    width: 27,
    resizeMode: 'contain',
  },
  loaderContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  // TEMPORARY: Test panel styles
  testButton: {
    backgroundColor: '#007bff',
    margin: 15,
    padding: 10,
    borderRadius: 8,
    alignItems: 'center',
  },
  testButtonText: {
    color: 'white',
    fontWeight: 'bold',
    fontSize: 16,
  },
  modalContainer: {
    flex: 1,
    backgroundColor: '#fff',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  closeButton: {
    backgroundColor: '#dc3545',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 6,
  },
  closeButtonText: {
    color: 'white',
    fontWeight: 'bold',
  },
});

export default CategoriesScreen;
