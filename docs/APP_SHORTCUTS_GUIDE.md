# Android App Shortcuts Implementation Guide

This guide explains how to implement and use pinned app shortcuts in your React Native Android application.

## Overview

App shortcuts allow users to create quick access icons on their home screen that directly open specific features within your app. This implementation supports:

- **Pinned Shortcuts**: User-created shortcuts that appear as separate icons on the home screen
- **Deep Linking**: Direct navigation to specific screens when shortcuts are tapped
- **Dynamic Creation**: Programmatic shortcut creation from within the app
- **Android Only**: This feature is specifically designed for Android devices

## Features Implemented

### 1. Company List Shortcuts
- Create shortcuts for specific category company lists
- Direct navigation to `CompanyScreen` with category ID and title
- Example: "Bank Companies" shortcut opens the bank category company list

### 2. Categories Shortcuts
- Create shortcuts to the main Categories screen
- Quick access to browse all available categories

### 3. Deep Link Handling
- Custom URL scheme: `indiacustomercare://`
- Automatic navigation to the correct screen when shortcuts are tapped
- Support for parameters (categoryId, title, etc.)

## Technical Implementation

### Android Native Components

#### 1. AppShortcutModule.kt
- Native Android module that bridges React Native with Android's ShortcutManager
- Handles shortcut creation, removal, and management
- Supports both modern (API 25+) and legacy shortcut methods

#### 2. AppShortcutPackage.kt
- React Native package registration for the native module
- Registered in MainApplication.kt

#### 3. MainActivity.kt Updates
- Deep link intent handling
- Sends deep link events to React Native when shortcuts are tapped

#### 4. AndroidManifest.xml Updates
- Added shortcut permissions
- Intent filter for deep link handling with custom scheme

### React Native Components

#### 1. AppShortcutService.js
- Main service class for shortcut operations
- Provides methods for creating, removing, and managing shortcuts
- Handles deep link parsing and navigation

#### 2. useAppShortcuts.js Hook
- React hook for easy shortcut integration
- Manages shortcut state and provides convenient methods
- Handles deep link navigation setup

#### 3. ShortcutButton.js Component
- Reusable UI component for shortcut creation
- Shows loading states and handles user interactions
- Android-only rendering (hidden on iOS)

## Usage Examples

### Creating a Company List Shortcut

```javascript
import { useAppShortcuts } from '../hooks/useAppShortcuts';

const MyScreen = ({ navigation }) => {
  const { createCompanyListShortcut, isShortcutSupported } = useAppShortcuts(navigation);

  const handleCreateShortcut = async () => {
    const success = await createCompanyListShortcut(categoryId, categoryName);
    if (success) {
      console.log('Shortcut created successfully');
    }
  };

  return (
    <View>
      {isShortcutSupported && (
        <ShortcutButton
          title="Add to Home Screen"
          onPress={handleCreateShortcut}
        />
      )}
    </View>
  );
};
```

### Creating a Categories Shortcut

```javascript
const { createCategoriesShortcut } = useAppShortcuts(navigation);

const handleCreateCategoriesShortcut = async () => {
  await createCategoriesShortcut();
};
```

## Deep Link URL Structure

### Company List Deep Link
```
indiacustomercare://companylist?categoryId=123&title=Bank%20Companies
```

### Categories Deep Link
```
indiacustomercare://categories
```

## Integration Points

### 1. CategoriesDetailsScreen (Company List)
- Added shortcut button after search bar
- Creates category-specific shortcuts
- Uses category ID and title from route parameters

### 2. CategoriesScreen (Main Categories)
- Added shortcut button after search bar
- Creates general categories shortcut
- Uses useAppNavigation hook for navigation access

## Android Permissions

The following permissions are added to AndroidManifest.xml:

```xml
<uses-permission android:name="com.android.launcher.permission.INSTALL_SHORTCUT" />
<uses-permission android:name="com.android.launcher.permission.UNINSTALL_SHORTCUT" />
```

## Device Compatibility

### Modern Android (API 25+)
- Uses ShortcutManager for dynamic shortcuts
- Supports pinned shortcuts with user confirmation
- Better integration with launcher

### Legacy Android (API < 25)
- Uses broadcast intents for shortcut creation
- Automatic shortcut creation without user confirmation
- Limited management capabilities

## User Experience

### Shortcut Creation Flow
1. User taps "Add to Home Screen" button
2. System shows confirmation dialog (modern Android)
3. User confirms shortcut creation
4. Shortcut appears on home screen
5. Success message displayed in app

### Shortcut Usage Flow
1. User taps shortcut icon on home screen
2. App opens (or comes to foreground)
3. Deep link is processed
4. App navigates to the specific screen
5. User sees the requested content immediately

## Error Handling

### Common Error Scenarios
- Device doesn't support shortcuts
- User denies shortcut creation permission
- Network issues during shortcut creation
- Invalid deep link parameters

### Error Messages
- "Pin shortcuts not supported on this device"
- "Failed to create shortcut. Please try again."
- "App shortcuts are not supported on this device"

## Testing

### Manual Testing Steps
1. Install app on Android device
2. Navigate to Categories screen
3. Tap "Add Categories to Home Screen"
4. Confirm shortcut creation
5. Go to home screen and verify shortcut appears
6. Tap shortcut and verify it opens Categories screen
7. Repeat for Company List shortcuts

### Testing Different Android Versions
- Test on Android 7.1+ (API 25+) for modern shortcuts
- Test on older Android versions for legacy shortcuts
- Verify error handling on unsupported devices

## Troubleshooting

### Shortcuts Not Appearing
- Check device supports shortcuts
- Verify permissions in AndroidManifest.xml
- Check launcher compatibility

### Deep Links Not Working
- Verify intent filter in AndroidManifest.xml
- Check URL scheme matches implementation
- Ensure MainActivity handles intents correctly

### Build Issues
- Verify AppShortcutPackage is registered in MainApplication.kt
- Check Kotlin compilation settings
- Ensure all imports are correct

## Future Enhancements

### Potential Improvements
1. **Static Shortcuts**: Pre-defined shortcuts in app manifest
2. **Shortcut Icons**: Custom icons for different shortcut types
3. **Shortcut Management**: UI to view and remove existing shortcuts
4. **Analytics**: Track shortcut usage and effectiveness
5. **Contextual Shortcuts**: Smart suggestions based on user behavior

### Additional Features
- Shortcut for specific companies
- Search shortcuts with pre-filled queries
- Recent items shortcuts
- Favorite categories shortcuts
