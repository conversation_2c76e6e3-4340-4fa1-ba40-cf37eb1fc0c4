package com.sourcecode

import android.content.Intent
import android.content.pm.ShortcutInfo
import android.content.pm.ShortcutManager
import android.graphics.drawable.Icon
import android.os.Build
import androidx.annotation.RequiresApi
import com.facebook.react.bridge.*
import com.facebook.react.modules.core.DeviceEventManagerModule

class AppShortcutModule(reactContext: ReactApplicationContext) : ReactContextBaseJavaModule(reactContext) {

    override fun getName(): String {
        return "AppShortcut"
    }

    @ReactMethod
    fun createShortcut(shortcutId: String, label: String, deepLink: String, promise: Promise) {
        try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N_MR1) {
                createDynamicShortcut(shortcutId, label, deepLink, promise)
            } else {
                createLegacyShortcut(label, deepLink, promise)
            }
        } catch (e: Exception) {
            promise.reject("SHORTCUT_ERROR", "Failed to create shortcut: ${e.message}", e)
        }
    }

    @RequiresApi(Build.VERSION_CODES.N_MR1)
    private fun createDynamicShortcut(shortcutId: String, label: String, deepLink: String, promise: Promise) {
        val activity = currentActivity
        if (activity == null) {
            promise.reject("NO_ACTIVITY", "No current activity available")
            return
        }

        val shortcutManager = activity.getSystemService(ShortcutManager::class.java)
        
        if (shortcutManager?.isRequestPinShortcutSupported == true) {
            val intent = Intent(Intent.ACTION_VIEW).apply {
                data = android.net.Uri.parse(deepLink)
                setClassName(activity.packageName, "com.sourcecode.MainActivity")
                flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TOP
            }

            val shortcut = ShortcutInfo.Builder(activity, shortcutId)
                .setShortLabel(label)
                .setLongLabel(label)
                .setIcon(Icon.createWithResource(activity, R.mipmap.ic_launcher))
                .setIntent(intent)
                .build()

            val callbackIntent = shortcutManager.createShortcutResultIntent(shortcut)
            val successCallback = android.app.PendingIntent.getBroadcast(
                activity, 0, callbackIntent, 
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                    android.app.PendingIntent.FLAG_IMMUTABLE
                } else {
                    0
                }
            )

            shortcutManager.requestPinShortcut(shortcut, successCallback.intentSender)
            promise.resolve("Shortcut creation requested")
        } else {
            promise.reject("NOT_SUPPORTED", "Pin shortcuts not supported on this device")
        }
    }

    private fun createLegacyShortcut(label: String, deepLink: String, promise: Promise) {
        val activity = currentActivity
        if (activity == null) {
            promise.reject("NO_ACTIVITY", "No current activity available")
            return
        }

        val shortcutIntent = Intent(Intent.ACTION_VIEW).apply {
            data = android.net.Uri.parse(deepLink)
            setClassName(activity.packageName, "com.sourcecode.MainActivity")
        }

        val addIntent = Intent().apply {
            putExtra(Intent.EXTRA_SHORTCUT_INTENT, shortcutIntent)
            putExtra(Intent.EXTRA_SHORTCUT_NAME, label)
            putExtra(Intent.EXTRA_SHORTCUT_ICON_RESOURCE,
                Intent.ShortcutIconResource.fromContext(activity, R.mipmap.ic_launcher))
            action = "com.android.launcher.action.INSTALL_SHORTCUT"
        }

        activity.sendBroadcast(addIntent)
        promise.resolve("Legacy shortcut created")
    }

    @ReactMethod
    fun removeShortcut(shortcutId: String, promise: Promise) {
        try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N_MR1) {
                val activity = currentActivity
                if (activity == null) {
                    promise.reject("NO_ACTIVITY", "No current activity available")
                    return
                }

                val shortcutManager = activity.getSystemService(ShortcutManager::class.java)
                shortcutManager?.removeDynamicShortcuts(listOf(shortcutId))
                promise.resolve("Shortcut removed")
            } else {
                promise.reject("NOT_SUPPORTED", "Remove shortcuts not supported on this Android version")
            }
        } catch (e: Exception) {
            promise.reject("SHORTCUT_ERROR", "Failed to remove shortcut: ${e.message}", e)
        }
    }

    @ReactMethod
    fun isShortcutSupported(promise: Promise) {
        try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N_MR1) {
                val activity = currentActivity
                if (activity == null) {
                    promise.resolve(false)
                    return
                }

                val shortcutManager = activity.getSystemService(ShortcutManager::class.java)
                promise.resolve(shortcutManager?.isRequestPinShortcutSupported ?: false)
            } else {
                // Legacy shortcuts are generally supported on older versions
                promise.resolve(true)
            }
        } catch (e: Exception) {
            promise.resolve(false)
        }
    }

    @ReactMethod
    fun getExistingShortcuts(promise: Promise) {
        try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N_MR1) {
                val activity = currentActivity
                if (activity == null) {
                    promise.reject("NO_ACTIVITY", "No current activity available")
                    return
                }

                val shortcutManager = activity.getSystemService(ShortcutManager::class.java)
                val shortcuts = shortcutManager?.dynamicShortcuts ?: emptyList()
                
                val shortcutArray = Arguments.createArray()
                shortcuts.forEach { shortcut ->
                    val shortcutMap = Arguments.createMap().apply {
                        putString("id", shortcut.id)
                        putString("shortLabel", shortcut.shortLabel?.toString())
                        putString("longLabel", shortcut.longLabel?.toString())
                    }
                    shortcutArray.pushMap(shortcutMap)
                }
                
                promise.resolve(shortcutArray)
            } else {
                promise.resolve(Arguments.createArray())
            }
        } catch (e: Exception) {
            promise.reject("SHORTCUT_ERROR", "Failed to get shortcuts: ${e.message}", e)
        }
    }
}
