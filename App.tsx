import React, {useEffect} from 'react';
import {NavigationContainer} from '@react-navigation/native';
import SplashScreen from 'react-native-splash-screen';
import {store} from './src/components/redux/store';

import NavStack from './src/navigation/NavStack';
import Toast from 'react-native-toast-message';
import {Provider} from 'react-redux';
import backgroundSyncManager from './src/services/backgroundSyncManager';

// Import first launch testing utilities for development
if (__DEV__) {
  require('./src/utils/testFirstLaunch');
}

const App = () => {
  useEffect(() => {
    // Hide splash screen immediately
    SplashScreen.hide();

    // Initialize background sync manager
    const initializeBackgroundSync = async () => {
      try {
        await backgroundSyncManager.initialize();
        console.log('[App] Background sync manager initialized');
      } catch (error) {
        console.error(
          '[App] Failed to initialize background sync manager:',
          error,
        );
      }
    };

    initializeBackgroundSync();

    // Cleanup on unmount
    return () => {
      backgroundSyncManager.cleanup();
    };
  }, []);

  return (
    <Provider store={store}>
      <NavigationContainer>
        <NavStack />
        <Toast />
      </NavigationContainer>
    </Provider>
  );
};

export default App;
